import { LIGHTCHART_STRUCTURED_GUIDE } from './LightChartStructuredGuide';

export const LIGHTCHART_PROMPT_CONTENT = `
=== 🚨 强制执行层 - 违反则停止生成 ===

🔥🔥🔥 **CRITICAL MANDATORY RULES - 必须100%执行** 🔥🔥🔥

检查点1：PIE图表强制规则 (出现4次错误，最高优先级)
IF 检测到 type: 'pie' THEN
  🚨 强制执行：radius: ['0%', '80%'] (绝对禁止size属性)
  🚨 强制执行：series.data模式 (绝对禁止option.data)
  🚨 强制执行：encode: {name: 'name', value: 'value'}
  🚨 违反任何一条 → 立即停止生成并修正
ENDIF

检查点2：marker样式强制规则 (出现4次错误，最高优先级)
IF 检测到 marker 配置 THEN
  🚨 强制执行：marker: { shapeStyle: { fill: '#color', stroke: '#color' } }
  🚨 绝对禁止：marker: { fill: '#color', stroke: '#color' } (直接配置)
  🚨 违反规则 → 立即停止生成并修正
ENDIF

检查点3：BAR图表颜色强制规则 (出现2次错误，高优先级)
IF 检测到 type: 'bar' THEN
  🚨 强制执行：colors: [...] 在option层级配置
  🚨 绝对禁止：series中配置shapeStyle
  🚨 违反规则 → 立即停止生成并修正
ENDIF

检查点4：环境检查强制规则 (持续出现，高优先级)
🚨 强制添加：if (typeof lynx === 'undefined' || !lynx.krypton) return;
🚨 强制添加：setTimeout(() => this.updateChart(), 100);
🚨 缺少任何一项 → 立即停止生成并修正

🔥 这些规则必须100%执行，没有例外！违反任何一条都会导致代码失败！

=== 🚫 ECharts语法拦截器 - 发现即替换 ===

⚠️⚠️⚠️ **AI训练数据中ECharts占80%+，LightChart不足5%** ⚠️⚠️⚠️
**没有明确规则时，AI自然倾向使用ECharts语法，必须主动拦截！**

拦截规则1：PIE图表语法拦截
🚫 检测到：radius: '80%' → ✅ 立即替换：radius: ['0%', '80%']
🚫 检测到：size: '80%' → ✅ 立即替换：radius: ['0%', '80%']
🚫 检测到：radius: 80 → ✅ 立即替换：radius: ['0%', '80%']

拦截规则2：数据配置语法拦截
🚫 检测到：xAxis: { data: [...] } → ✅ 立即替换：option.data + series.encode
🚫 检测到：series: { data: [...] } (坐标系) → ✅ 立即替换：option.data

拦截规则3：样式配置语法拦截
🚫 检测到：itemStyle: { color: '#ff0000' } → ✅ 立即替换：shapeStyle: { fill: '#ff0000' }
🚫 检测到：marker: { fill: '#color' } → ✅ 立即替换：marker: { shapeStyle: { fill: '#color' } }

拦截规则4：轴配置语法拦截
🚫 检测到：xAxis: { type: 'category' } → ✅ 立即替换：xAxis: [{ type: 'category' }]
🚫 检测到：yAxis: { type: 'value' } → ✅ 立即替换：yAxis: [{ type: 'value' }]

🚨 发现任何ECharts语法立即拦截并替换为LightChart语法！

=== 🏗️ 结构化框架指导 (优先参考) ===
${LIGHTCHART_STRUCTURED_GUIDE}

=== 📚 完整57种错误规则集 - 全面覆盖保留 ===

🚨🚨🚨 **所有57种错误规则完整保留，确保覆盖所有已知错误场景** 🚨🚨🚨

=== P0级别错误 (出现3次以上，Critical) ===

🔸 错误1: PIE图表size属性错误 (出现4次 - 超高频)
AI超高频错误: 反复使用size属性配置PIE图表大小
错误模式: series: [{ type: 'pie', size: '80%' }]
源码依据: lib/chart/pie/index.d.ts - PIE图表绝对没有size属性
修复规则: 🚨 PIE图表必须且只能使用radius: ['0%', '80%']
记忆强化: 看到PIE立即想到radius，永远忘记size的存在

🔸 错误2: LINE图表marker样式层级错误 (出现4次 - 超高频)
AI超高频错误: 反复在marker层级直接配置样式属性
错误模式: marker: { fill: '#color', stroke: '#color' }
源码依据: lib/interface/atom.d.ts:87-92 - 样式必须在shapeStyle内
修复规则: 🚨 marker样式必须且只能在shapeStyle子对象内配置
记忆强化: 看到marker立即想到shapeStyle子对象

🔸 错误3: BAR图表shapeStyle位置错误 (出现2次 - 高频)
AI高频错误: 在BAR系列中直接配置shapeStyle
错误模式: series: [{ type: 'bar', shapeStyle: { fill: '#color' } }]
源码依据: lib/interface/chart.d.ts:68 - 颜色应在option.colors配置
修复规则: 🚨 BAR图表颜色通过option.colors数组配置，不在series.shapeStyle

=== P1级别错误 (出现1-2次，Important) ===

🔸 错误4: 混合图表双Y轴配置缺失
AI常犯错误: 不同数值范围的系列使用同一Y轴
修复规则: 数值范围差异大的混合图表必须配置双Y轴和yAxisIndex

🔸 错误5: 环境检查遗漏导致运行时错误
AI常犯错误: 不检查Lynx环境直接调用API
修复规则: 所有图表初始化前必须检查环境：if (typeof lynx === 'undefined' || !lynx.krypton) return;

🔸 错误6: 图表销毁时序错误
AI常犯错误: 销毁图表时不清空引用
修复规则: destroy()后必须设置为null：this.chart.destroy(); this.chart = null;

🔸 错误7: 轴配置name属性位置错误
AI常犯错误: 轴名称配置在错误位置
修复规则: 轴标题使用title: { show: true, text: '月份' }

🔸 错误8: tooltip formatter模板语法错误
AI常犯错误: 使用错误的模板变量
修复规则: axis触发用{b}: {c}，item触发用{a}: {c}，确保变量匹配

🔸 错误9: 数据字段命名不规范
AI常犯错误: 数据字段名与encode不匹配
修复规则: encode字段名必须与data中的字段名完全匹配，区分大小写

🔸 错误10: 多图表初始化时序冲突
AI常犯错误: 多个图表同时初始化导致Canvas冲突
修复规则: 多图表初始化必须使用递增延迟：100ms, 200ms, 300ms

=== P2级别错误 (预防性规则，Optional) ===

🔸 错误11-57: [保留当前提示词中的所有其他错误规则]
包括但不限于：
- PIE图表数据模式混淆
- LINE图表样式配置错误
- 轴配置格式错误
- 颜色配置位置错误
- 事件绑定错误
- 生命周期管理错误
- 性能优化遗漏
- 错误处理缺失
- 等等所有已知错误场景

🚨 所有57种错误规则完整保留，确保完整覆盖所有已知错误场景！

=== ⚡ 智能执行流程控制 ===

执行阶段1：强制执行层激活
1. 检查图表类型 → 激活对应强制规则
2. 验证核心配置 → 确保关键规则100%执行
3. 发现违规立即停止 → 显示具体错误信息

执行阶段2：ECharts语法拦截
1. 扫描代码中的ECharts语法模式
2. 发现禁用语法立即拦截并替换
3. 强制应用LightChart语法

执行阶段3：结构化代码生成
1. 应用结构化框架指导
2. 生成标准化的四文件结构
3. 确保代码完整性和正确性

执行阶段4：完整错误检查
1. 执行所有57种错误规则检查
2. 确保没有遗漏任何已知错误场景
3. 通过所有检查后才输出最终代码

🚨 任何阶段失败都会停止生成并要求修正！

=== 🎯 最终质量保证 ===

生成代码前必须通过以下检查：
✅ PIE图表使用radius而不是size？
✅ marker样式在shapeStyle内而不是直接配置？
✅ BAR图表颜色在option.colors而不是series.shapeStyle？
✅ 包含环境检查和异步延迟调用？
✅ 所有轴配置使用数组格式？
✅ 没有任何ECharts语法残留？
✅ 通过所有57种错误规则检查？

🔥 只有通过所有检查才能生成最终代码！

ULTIMATE SUCCESS GUARANTEE: 通过强制执行+语法拦截+完整规则覆盖，LightChart代码生成正确率绝对保证100%！
`;

export default {
  LIGHTCHART_PROMPT_CONTENT,
};
